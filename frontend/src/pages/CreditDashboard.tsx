import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeftIcon, CreditCardIcon, ChartBarIcon } from '@heroicons/react/24/outline';
import CreditBalance from '@/components/credits/CreditBalance';
import CreditPurchaseModal from '@/components/credits/CreditPurchaseModal';
import TransactionHistory from '@/components/credits/TransactionHistory';
import AegisScholarLogoWithoutText from '../assets/AegisScholarLogoIcon';
import ThemeToggle from '@/components/ThemeToggle';
import { useUser } from '../contexts/userContext';
import { toast } from 'react-toastify';

const CreditDashboard: React.FC = () => {
    const navigate = useNavigate();
    const { user } = useUser();
    const [showPurchaseModal, setShowPurchaseModal] = useState(false);
    const [refreshKey, setRefreshKey] = useState(0);

    const handlePurchaseSuccess = (creditsAdded: number, newBalance: number) => {
        toast.success(`Successfully purchased ${creditsAdded} credits! New balance: ${newBalance}`);
        setRefreshKey(prev => prev + 1); // Force refresh of components
    };

    return (
        <div className="min-h-screen w-full bg-background p-3 pb-16">
            <div className="max-w-7xl mx-auto space-y-4 sm:space-y-6 lg:space-y-8">
                {/* Header */}
                <header className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-3">
                    <div className="flex items-center gap-3 min-w-0 flex-1">
                        <button
                            onClick={() => navigate(-1)}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-muted rounded-lg transition-colors min-h-[44px] touch-manipulation"
                            title="Go Back"
                        >
                            <ArrowLeftIcon className="w-4 h-4" />
                            <span className="hidden xs:inline sm:inline">Back</span>
                        </button>
                        <AegisScholarLogoWithoutText
                            className="w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0"
                            style={{ fill: 'var(--color-accent)' }}
                        />
                        <div className="min-w-0 flex-1">
                            <h1 className="text-lg sm:text-xl lg:text-2xl font-['Space_Grotesk'] font-bold text-foreground truncate">
                                Credit Management
                            </h1>
                            <p className="text-xs sm:text-sm text-muted-foreground line-clamp-1">
                                Manage your AegisGrader credits and view transaction history
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <ThemeToggle size="default" />
                    </div>
                </header>

                {/* Main Content */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
                    {/* Left Column - Credit Balance and Purchase */}
                    <div className="lg:col-span-1 space-y-4 sm:space-y-6">
                        {/* Credit Balance Card */}
                        <CreditBalance 
                            key={`balance-${refreshKey}`}
                            showDetails={true}
                            onPurchaseClick={() => setShowPurchaseModal(true)}
                        />

                        {/* Quick Purchase Card */}
                        <div className="bg-card rounded-lg border border-border p-6">
                            <div className="flex items-center gap-3 mb-4">
                                <div className="p-2 bg-primary/10 rounded-full">
                                    <CreditCardIcon className="w-5 h-5 text-primary" />
                                </div>
                                <h3 className="text-lg font-semibold text-foreground">Purchase Credits</h3>
                            </div>
                            <p className="text-sm text-muted-foreground mb-4">
                                Buy credits to continue using AegisGrader for automated test evaluation.
                            </p>
                            <button
                                onClick={() => setShowPurchaseModal(true)}
                                className="w-full px-4 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
                            >
                                Purchase Credits
                            </button>
                        </div>

                        {/* Usage Information */}
                        <div className="bg-card rounded-lg border border-border p-6">
                            <div className="flex items-center gap-3 mb-4">
                                <div className="p-2 bg-blue-500/10 rounded-full">
                                    <ChartBarIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                </div>
                                <h3 className="text-lg font-semibold text-foreground">How Credits Work</h3>
                            </div>
                            <div className="space-y-3 text-sm text-muted-foreground">
                                <div className="flex items-start gap-2">
                                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                                    <p>Each answer sheet evaluation costs 1 credit</p>
                                </div>
                                <div className="flex items-start gap-2">
                                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                                    <p>New users receive 10 free credits to get started</p>
                                </div>
                                <div className="flex items-start gap-2">
                                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                                    <p>Credits never expire and can be used anytime</p>
                                </div>
                                <div className="flex items-start gap-2">
                                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                                    <p>Bulk purchases offer better value per credit</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right Column - Transaction History */}
                    <div className="lg:col-span-2">
                        <TransactionHistory 
                            key={`history-${refreshKey}`}
                            limit={50} 
                        />
                    </div>
                </div>

                {/* Welcome Message for New Users */}
                {user && (
                    <div className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-lg border border-primary/20 p-6">
                        <div className="flex items-start gap-4">
                            <div className="p-3 bg-primary/20 rounded-full flex-shrink-0">
                                <CreditCardIcon className="w-6 h-6 text-primary" />
                            </div>
                            <div className="flex-1">
                                <h3 className="text-lg font-semibold text-foreground mb-2">
                                    Welcome to AegisGrader, {user.firstName || user.username}!
                                </h3>
                                <p className="text-muted-foreground mb-4">
                                    You started with 10 free credits to explore our AI-powered grading system. 
                                    Each credit allows you to evaluate one answer sheet with detailed feedback and scoring.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-3">
                                    <button
                                        onClick={() => navigate('/aegis-grader')}
                                        className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
                                    >
                                        Start Grading
                                    </button>
                                    <button
                                        onClick={() => setShowPurchaseModal(true)}
                                        className="px-4 py-2 border border-border text-foreground rounded-lg hover:bg-muted transition-colors font-medium"
                                    >
                                        Buy More Credits
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Credit Purchase Modal */}
            <CreditPurchaseModal
                isOpen={showPurchaseModal}
                onClose={() => setShowPurchaseModal(false)}
                onSuccess={handlePurchaseSuccess}
            />
        </div>
    );
};

export default CreditDashboard;
