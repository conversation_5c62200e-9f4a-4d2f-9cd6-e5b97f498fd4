import React, { useState } from 'react';
import {
    CreditCardIcon,
    ChartBarIcon,
    CalendarIcon,
    Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { PaymentTransaction } from '@/types/billing';
import BillingStats from '@/components/billing/BillingStats';
import PaymentHistory from '@/components/billing/PaymentHistory';
import CreditUsageAnalytics from '@/components/billing/CreditUsageAnalytics';
import ReceiptModal from '@/components/billing/ReceiptModal';
import BillingErrorBoundary from '@/components/billing/BillingErrorBoundary';
import CreditPurchaseModal from '@/components/credits/CreditPurchaseModal';
import { toast } from 'react-toastify';

const BillingDashboard: React.FC = () => {
    const [activeTab, setActiveTab] = useState<'overview' | 'history' | 'analytics'>('overview');
    const [selectedTransaction, setSelectedTransaction] = useState<PaymentTransaction | null>(null);
    const [showReceiptModal, setShowReceiptModal] = useState(false);
    const [showPurchaseModal, setShowPurchaseModal] = useState(false);

    const tabs = [
        {
            id: 'overview' as const,
            label: 'Overview',
            icon: CreditCardIcon,
            description: 'Credit balance and billing summary'
        },
        {
            id: 'history' as const,
            label: 'Payment History',
            icon: CalendarIcon,
            description: 'All transactions and receipts'
        },
        {
            id: 'analytics' as const,
            label: 'Usage Analytics',
            icon: ChartBarIcon,
            description: 'Credit usage patterns and insights'
        }
    ];

    const handleTransactionClick = (transaction: PaymentTransaction) => {
        if (transaction.payment?.razorpayPaymentId) {
            setSelectedTransaction(transaction);
            setShowReceiptModal(true);
        } else {
            toast.info('Receipt not available for this transaction');
        }
    };

    const handlePurchaseSuccess = (creditsAdded: number, newBalance: number) => {
        toast.success(`Successfully purchased ${creditsAdded} credits! New balance: ${newBalance}`);
        // Refresh the current tab data
        window.location.reload();
    };

    return (
        <BillingErrorBoundary>
            <div className="min-h-screen bg-background p-2 pb-16">
            {/* Header */}
            <div className="mb-6">
                <div className="flex items-center gap-4 mb-4">
                    <div>
                        <h1 className="text-2xl lg:text-3xl font-['Space_Grotesk'] font-bold text-foreground">
                            Billing & Usage
                        </h1>
                        <p className="text-muted-foreground">
                            Manage your credits, view payment history, and track usage analytics
                        </p>
                    </div>
                </div>

                {/* Tab Navigation */}
                <div className="flex flex-col sm:flex-row gap-2">
                    {tabs.map((tab) => (
                        <button
                            key={tab.id}
                            onClick={() => setActiveTab(tab.id)}
                            className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 text-left ${
                                activeTab === tab.id
                                    ? 'bg-primary text-primary-foreground shadow-lg'
                                    : 'bg-card text-foreground hover:bg-secondary border border-border'
                            }`}
                        >
                            <tab.icon className="w-5 h-5 flex-shrink-0" />
                            <div className="min-w-0">
                                <div className="font-medium">{tab.label}</div>
                                <div className={`text-xs ${
                                    activeTab === tab.id 
                                        ? 'text-primary-foreground/80' 
                                        : 'text-muted-foreground'
                                }`}>
                                    {tab.description}
                                </div>
                            </div>
                        </button>
                    ))}
                </div>
            </div>

            {/* Tab Content */}
            <div className="space-y-6">
                {activeTab === 'overview' && (
                    <div className="space-y-6">
                        <BillingStats
                            showDetailedStats={true}
                            className="w-full"
                        />

                        {/* Quick Purchase Action */}
                        <div className="bg-card rounded-lg border border-border p-6">
                            <div className="flex items-center gap-3 mb-4">
                                <CreditCardIcon className="w-6 h-6 text-primary" />
                                <h3 className="text-lg font-semibold text-foreground">Purchase Credits</h3>
                            </div>
                            <p className="text-muted-foreground mb-4">
                                Buy more credits to continue using AegisGrader for exam evaluations.
                            </p>
                            <button
                                onClick={() => setShowPurchaseModal(true)}
                                className="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                            >
                                Buy Credits
                            </button>
                        </div>
                    </div>
                )}

                {activeTab === 'history' && (
                    <PaymentHistory 
                        showFilters={true}
                        limit={20}
                        onTransactionClick={handleTransactionClick}
                    />
                )}

                {activeTab === 'analytics' && (
                    <CreditUsageAnalytics 
                        showAnalytics={true}
                        timeRange="month"
                    />
                )}
            </div>

            {/* Modals */}
            {showReceiptModal && selectedTransaction && (
                <ReceiptModal
                    isOpen={showReceiptModal}
                    onClose={() => {
                        setShowReceiptModal(false);
                        setSelectedTransaction(null);
                    }}
                    transaction={selectedTransaction}
                    onDownload={() => {
                        toast.success('Receipt downloaded successfully');
                    }}
                />
            )}

            <CreditPurchaseModal
                isOpen={showPurchaseModal}
                onClose={() => setShowPurchaseModal(false)}
                onSuccess={handlePurchaseSuccess}
            />
            </div>
        </BillingErrorBoundary>
    );
};

export default BillingDashboard;
