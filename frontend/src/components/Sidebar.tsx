import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  UserIcon,
  ChartBarIcon,
  CpuChipIcon,
  PlusCircleIcon,
  SparklesIcon,
  ClipboardDocumentCheckIcon,
  CreditCardIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  UserIcon as UserIconSolid,
  DocumentPlusIcon as DocumentPlusIconSolid,
  PlusCircleIcon as PlusCircleIconSolid,
  ChartBarIcon as ChartBarIconSolid,
  ArrowLeftStartOnRectangleIcon as ArrowLeftStartOnRectangleIconSolid,
  CpuChipIcon as CpuChipIconSolid,
  SparklesIcon as SparklesIconSolid,
  ClipboardDocumentCheckIcon as ClipboardDocumentCheckIconSolid
} from '@heroicons/react/24/solid';

import { useUser } from '../contexts/userContext';
import AegisScholarLogoWithoutText from '../assets/AegisScholarLogoIcon';
import debounce from '../lib/debounce';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { FileCheck2, PanelLeftClose, PanelLeftOpen } from 'lucide-react';
import ThemeToggle from './ThemeToggle';

interface SidebarProps {
  isExpanded: boolean;
  onToggle: () => void;
}

interface NavItem {
  icon: React.ElementType;
  solidIcon: React.ElementType;
  label: string;
  path: string;
}

const Sidebar: React.FC<SidebarProps> = ({ isExpanded, onToggle }) => {
  const [isMobile, setIsMobile] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, setUser } = useUser();

  const RawItems: NavItem[] = [
    // { icon: SparklesIcon, solidIcon: SparklesIconSolid, label: "AegisAI", path: "/aegis-ai" },
    // { icon: HomeIcon, solidIcon: HomeIconSolid, label: "Dashboard", path: `/${user?.role?.toLowerCase()}-dashboard` },
    // { icon: PlusCircleIcon, solidIcon: PlusCircleIconSolid, label: "Create", path: "/schedule-test" },
    // { icon: ChartBarIcon, solidIcon: ChartBarIconSolid, label: "Analytics", path: "/analytics" },
    { icon: HomeIcon, solidIcon: HomeIconSolid, label: "AegisGrader", path: "/aegis-grader" },
    { icon: ClipboardDocumentCheckIcon, solidIcon: ClipboardDocumentCheckIconSolid, label: "Submissions", path: "/grading-submissions" },
    { icon: CreditCardIcon, solidIcon: CreditCardIcon, label: "Plans & Billing", path: "/billing" },
    // { icon: UserIcon, solidIcon: UserIconSolid, label: "Profile", path: "/profile" },
  ];

  const items = RawItems.filter((item) => {
    if (user?.role === 'Teacher' && item.label === 'Practice') {
      return false;
    }
    if (user?.role === 'Student' && item.label === 'Analytics') {
      return false;
    }
    if (user?.role === 'Student' && item.label === 'Create' && !user?.schoolCode) {
      return true;
    }
    if (user?.role === 'Student' && item.label === 'Create') {
      return false;
    }
    if (item.label === 'AegisGrader' && user?.role === 'Student') {
      return false;
    }
    if (item.label === 'AegisGrader' && process.env.NODE_ENV === 'production') {
      return false;
    }
    // if (item.label === 'AegisAI' && user?.role === 'Teacher') {
    //   return false;
    // }
    // AegisAI is now available for all users
    return true;
  });

  const axiosPrivate = useAxiosPrivate();

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const mobileBreakpoint = 640;

      setIsMobile(width < mobileBreakpoint);

      if (width < mobileBreakpoint && isExpanded) {
        onToggle();
      }
    };

    handleResize();

    const debouncedResize = debounce(handleResize, 150);
    window.addEventListener('resize', debouncedResize);

    return () => {
      window.removeEventListener('resize', debouncedResize);
      debouncedResize.cancel();
    };
  }, [isExpanded, onToggle]);

  const MenuItem = ({ item }: { item: NavItem }) => {
    const Icon = item.icon;
    const SolidIcon = item.solidIcon;
    const isActive = location.pathname === item.path ||
      (item.path.includes('-dashboard') && location.pathname.includes('-dashboard')) ||
      (item.path === '/aegis-ai' && (location.pathname === '/' || location.pathname === '/aegis-ai'));

    return (
      <div className="relative group">
        <button
          onClick={() => navigate(item.path)}
          className={`
            w-full flex items-center p-2 sm:p-3 rounded-lg
            transition-all duration-200 ease-in-out dark:border-none
            ${isActive
              ? 'bg-card text-primary' 
              : 'text-foreground hover:bg-card'
            }
          `}
        >
          <div className="flex items-center justify-center w-5 h-5 shrink-0">
            {isActive ? (
              <SolidIcon className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
            ) : (
              <Icon className="w-4 h-4 sm:w-5 sm:h-5 text-foreground" />
            )}
          </div>
          <span className={`
            text-xs sm:text-sm font-medium whitespace-nowrap
            transition-all duration-200 ease-in-out
            ${isExpanded ? 'opacity-100 ml-2 translate-x-0' : 'opacity-0 -translate-x-2'}
          `}>
            {item.label}
          </span>
        </button>
        <div className={`
          absolute left-full top-2 ml-2 px-2 py-1
          bg-muted text-muted-foreground text-xs sm:text-sm rounded
          transition-opacity duration-200 whitespace-nowrap z-50
          ${!isExpanded
            ? 'opacity-0 group-hover:opacity-100 pointer-events-none'
            : 'opacity-0 pointer-events-none'
          }
        `}>
          {item.label}
        </div>
      </div>
    );
  };

  if (isMobile) {
    return (
      <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-border shadow-lg z-50">
        <div className="flex justify-around items-center h-14 sm:h-16 px-2">
          {items.map((item) => {
            const Icon = item.icon;
            const SolidIcon = item.solidIcon;
            const isActive = location.pathname === item.path ||
              (item.path.includes('-dashboard') && location.pathname.includes('-dashboard')) ||
              (item.path === '/aegis-ai' && (location.pathname === '/' || location.pathname === '/aegis-ai'));

            return (
              <button
                key={item.path}
                onClick={() => navigate(item.path)}
                className={`
                  p-2 rounded-lg transition-colors duration-200
                  flex flex-col items-center justify-center
                  ${isActive ? 'text-primary' : 'text-foreground hover:text-primary'}
                `}
              >
                {isActive ? (
                  <SolidIcon className="w-5 h-5" />
                ) : (
                  <Icon className="w-5 h-5" />
                )}
                <span className="text-xs mt-1">{item.label}</span>
              </button>
            );
          })}
        </div>
      </div>
    );
  }

  return (
    <div className={`
      fixed left-0 top-0 h-screen bg-background border-r border-border
      transition-all duration-200 ease-in-out z-50
      ${isExpanded ? 'w-48 sm:w-56 lg:w-64' : 'w-14 sm:w-16'}
    `}>
      <div className="h-14 z-0 sm:h-16 flex items-center justify-center px-4 border-b border-border relative">
        <div className={`
          absolute transition-opacity duration-200
          ${!isExpanded ? 'opacity-100' : 'opacity-0'}
        `}>
          <AegisScholarLogoWithoutText
            className="w-10 h-10"
            style={{ fill: 'var(--color-accent)' }}
          />
        </div>

        <div className={`
          absolute flex items-center space-x-2 transition-opacity duration-200
          ${isExpanded ? 'opacity-100' : 'opacity-0'}
        `}>
          <AegisScholarLogoWithoutText
            className="w-10 h-10"
            style={{ fill: 'var(--color-accent)' }}
          />
          <span className="text-lg font-medium font-['Space_Grotesk'] text-primary whitespace-nowrap">
            AegisScholar
          </span>
        </div>
      </div>

      <nav className="flex flex-col gap-1 p-2">
        {items.map((item) => (
          <MenuItem key={item.path} item={item} />
        ))}
      </nav>

      {/* Theme Toggle */}
      <div className="absolute bottom-20 left-4">
        <ThemeToggle size="default" />
      </div>

      <button
        onClick={onToggle}
        className="
          absolute bottom-6 left-4
          p-1.5 bg-card border border-border-200 rounded-lg
          shadow-sm hover:shadow-sm transition-all duration-200
          flex items-center justify-center
          group
        "
      >
        <div className={`
          absolute left-full top-1 ml-2 px-2 py-1
          bg-muted text-muted-foreground text-xs sm:text-sm rounded
          transition-opacity duration-200 whitespace-nowrap z-50
          ${!isExpanded
            ? 'opacity-0 group-hover:opacity-100 pointer-events-auto'
            : 'opacity-0 group-hover:opacity-100 pointer-events-none'
          }
        `}>
          {isExpanded ? 'Collapse' : 'Expand'}
        </div>
        {isExpanded ? (
          <PanelLeftClose className="w-5 h-5 text-foreground dark:text-primary" />
        ) : (
          <PanelLeftOpen className="w-5 h-5 text-foreground dark:text-primary" />
        )}
      </button>
    </div>
  );
};

export default Sidebar;
