import React, { useState, useEffect } from 'react';
import {
    ChartBarIcon,
    ClockIcon,
    ArrowTrendingUpIcon,
    CalendarIcon,
    ArrowPathIcon,
    FunnelIcon
} from '@heroicons/react/24/outline';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { CreditUsageProps, UsageAnalytics, PaymentAnalytics } from '@/types/billing';

const CreditUsageAnalytics: React.FC<CreditUsageProps> = ({ 
    className = "",
    showAnalytics = true,
    timeRange = 'month'
}) => {
    const [usageAnalytics, setUsageAnalytics] = useState<UsageAnalytics | null>(null);
    const [paymentAnalytics, setPaymentAnalytics] = useState<PaymentAnalytics | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
    const axiosPrivate = useAxiosPrivate();

    const fetchAnalytics = async (range: string = selectedTimeRange) => {
        try {
            setLoading(true);
            setError(null);
            const response = await axiosPrivate.get(`/api/credits/analytics?timeRange=${range}`);
            setUsageAnalytics(response.data.data.usageAnalytics);
            setPaymentAnalytics(response.data.data.paymentAnalytics);
        } catch (err: any) {
            console.error('Error fetching analytics:', err);
            setError(err.response?.data?.message || 'Failed to fetch analytics data');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchAnalytics();
    }, []);

    const handleTimeRangeChange = (range: string) => {
        const validRange = range as 'week' | 'month' | 'quarter' | 'year';
        setSelectedTimeRange(validRange);
        fetchAnalytics(range);
    };

    const formatAmount = (amount: number) => {
        return `₹${(amount / 100).toFixed(2)}`;
    };

    const timeRangeOptions = [
        { value: 'week', label: 'Last Week' },
        { value: 'month', label: 'Last Month' },
        { value: 'quarter', label: 'Last 3 Months' },
        { value: 'year', label: 'Last Year' }
    ];

    if (loading) {
        return (
            <div className={`bg-card rounded-lg border border-border p-6 ${className}`}>
                <div className="flex items-center justify-center py-8">
                    <ArrowPathIcon className="w-6 h-6 animate-spin text-primary" />
                    <span className="ml-2 text-muted-foreground">Loading analytics...</span>
                </div>
            </div>
        );
    }

    if (error || !usageAnalytics) {
        return (
            <div className={`bg-card rounded-lg border border-destructive/50 p-6 ${className}`}>
                <div className="text-center py-8">
                    <p className="text-destructive mb-3">{error || 'Failed to load analytics'}</p>
                    <button 
                        onClick={() => fetchAnalytics()}
                        className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2 mx-auto"
                    >
                        <ArrowPathIcon className="w-4 h-4" />
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className={`space-y-6 ${className}`}>
            {/* Header with Time Range Selector */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <ChartBarIcon className="w-6 h-6 text-primary" />
                    <h2 className="text-xl font-semibold text-foreground">Usage Analytics</h2>
                </div>
                <div className="flex items-center gap-2">
                    <FunnelIcon className="w-4 h-4 text-muted-foreground" />
                    <select
                        value={selectedTimeRange}
                        onChange={(e) => handleTimeRangeChange(e.target.value)}
                        className="px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
                    >
                        {timeRangeOptions.map(option => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-card rounded-lg border border-border p-4">
                    <div className="flex items-center gap-3">
                        <div className="p-2 rounded-full bg-blue-50 dark:bg-blue-900/20">
                            <ChartBarIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <p className="text-sm text-muted-foreground">Total Credits Used</p>
                            <p className="text-2xl font-bold text-foreground">{usageAnalytics.totalCreditsUsed}</p>
                        </div>
                    </div>
                </div>

                <div className="bg-card rounded-lg border border-border p-4">
                    <div className="flex items-center gap-3">
                        <div className="p-2 rounded-full bg-green-50 dark:bg-green-900/20">
                            <ClockIcon className="w-5 h-5 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                            <p className="text-sm text-muted-foreground">Total Evaluations</p>
                            <p className="text-2xl font-bold text-foreground">{usageAnalytics.totalEvaluations}</p>
                        </div>
                    </div>
                </div>

                <div className="bg-card rounded-lg border border-border p-4">
                    <div className="flex items-center gap-3">
                        <div className="p-2 rounded-full bg-purple-50 dark:bg-purple-900/20">
                            <ArrowTrendingUpIcon className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                        </div>
                        <div>
                            <p className="text-sm text-muted-foreground">Avg. Credits/Day</p>
                            <p className="text-2xl font-bold text-foreground">{usageAnalytics.averageCreditsPerDay}</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Usage by Feature */}
            <div className="bg-card rounded-lg border border-border p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">Usage by Feature</h3>
                <div className="space-y-4">
                    {Object.entries(usageAnalytics.usageByFeature).map(([feature, data]) => (
                        <div key={feature} className="space-y-2">
                            <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-foreground">{feature}</span>
                                <div className="text-right">
                                    <span className="text-sm font-medium text-foreground">{data.credits} credits</span>
                                    <span className="text-xs text-muted-foreground ml-2">
                                        ({data.count} evaluations)
                                    </span>
                                </div>
                            </div>
                            <div className="w-full bg-secondary rounded-full h-2">
                                <div 
                                    className="bg-primary h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${data.percentage}%` }}
                                />
                            </div>
                            <div className="flex justify-between text-xs text-muted-foreground">
                                <span>{data.percentage.toFixed(1)}% of total usage</span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Recent Usage Trend */}
            {showAnalytics && usageAnalytics.dailyUsage.length > 0 && (
                <div className="bg-card rounded-lg border border-border p-6">
                    <h3 className="text-lg font-semibold text-foreground mb-4">Recent Usage Trend</h3>
                    <div className="space-y-3">
                        {usageAnalytics.dailyUsage.slice(-7).map((day, index) => (
                            <div key={index} className="flex items-center justify-between py-2">
                                <div className="flex items-center gap-3">
                                    <CalendarIcon className="w-4 h-4 text-muted-foreground" />
                                    <span className="text-sm text-foreground">
                                        {new Date(day.date).toLocaleDateString('en-IN', {
                                            month: 'short',
                                            day: 'numeric'
                                        })}
                                    </span>
                                </div>
                                <div className="text-right">
                                    <span className="text-sm font-medium text-foreground">
                                        {day.credits} credits
                                    </span>
                                    <span className="text-xs text-muted-foreground ml-2">
                                        ({day.evaluations} evaluations)
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Payment Analytics Summary */}
            {showAnalytics && paymentAnalytics && (
                <div className="bg-card rounded-lg border border-border p-6">
                    <h3 className="text-lg font-semibold text-foreground mb-4">Purchase Summary</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-3">
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Total Amount Spent:</span>
                                <span className="font-medium">{formatAmount(paymentAnalytics.totalAmountSpent)}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Credits Purchased:</span>
                                <span className="font-medium">{paymentAnalytics.totalCreditspurchased}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Average Order Value:</span>
                                <span className="font-medium">{formatAmount(paymentAnalytics.averageOrderValue)}</span>
                            </div>
                        </div>
                        <div className="space-y-3">
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Most Popular Package:</span>
                                <span className="font-medium">{paymentAnalytics.mostPopularPackage}</span>
                            </div>
                            {Object.entries(paymentAnalytics.purchasesByPackage).map(([packageType, data]) => (
                                <div key={packageType} className="text-xs text-muted-foreground">
                                    {packageType}: {data.count} purchases ({data.percentage.toFixed(1)}%)
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* Usage Insights */}
            <div className="bg-secondary/20 rounded-lg p-4">
                <h4 className="font-medium text-foreground mb-2">💡 Usage Insights</h4>
                <div className="space-y-2 text-sm text-muted-foreground">
                    <p>
                        • You've used <strong>{usageAnalytics.totalCreditsUsed}</strong> credits for{' '}
                        <strong>{usageAnalytics.totalEvaluations}</strong> evaluations
                    </p>
                    <p>
                        • Your most used feature is <strong>{usageAnalytics.mostUsedFeature}</strong>
                    </p>
                    <p>
                        • You average <strong>{usageAnalytics.averageCreditsPerDay}</strong> credits per day
                    </p>
                </div>
            </div>
        </div>
    );
};

export default CreditUsageAnalytics;
