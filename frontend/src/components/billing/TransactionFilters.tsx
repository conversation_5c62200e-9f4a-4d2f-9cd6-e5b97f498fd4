import React from 'react';
import { 
    FunnelIcon, 
    XMarkIcon,
    CalendarIcon,
    CreditCardIcon,
    CheckCircleIcon,
    ClockIcon,
    ExclamationCircleIcon,
    XCircleIcon
} from '@heroicons/react/24/outline';
import { TransactionFilterProps, TransactionFilters } from '@/types/billing';

const TransactionFilterComponent: React.FC<TransactionFilterProps> = ({
    filters,
    onFiltersChange,
    onReset,
    className = ""
}) => {
    const handleFilterChange = (key: keyof TransactionFilters, value: any) => {
        onFiltersChange({
            ...filters,
            [key]: value
        });
    };

    const hasActiveFilters = Object.values(filters).some(value => 
        value !== undefined && value !== '' && value !== null
    );

    const transactionTypes = [
        { value: 'PURCHASE', label: 'Purchases', icon: CreditCardIcon },
        { value: 'USAGE', label: 'Usage', icon: ClockIcon },
        { value: 'REFUND', label: 'Refunds', icon: ExclamationCircleIcon },
        { value: 'BONUS', label: 'Bonus', icon: CheckCircleIcon },
        { value: 'INITIAL_GRANT', label: 'Initial Credits', icon: CheckCircleIcon }
    ];

    const transactionStatuses = [
        { value: 'COMPLETED', label: 'Completed', icon: CheckCircleIcon },
        { value: 'PENDING', label: 'Pending', icon: ClockIcon },
        { value: 'FAILED', label: 'Failed', icon: XCircleIcon },
        { value: 'CANCELLED', label: 'Cancelled', icon: XMarkIcon }
    ];

    return (
        <div className={`bg-card rounded-lg border border-border p-4 ${className}`}>
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                    <FunnelIcon className="w-5 h-5 text-muted-foreground" />
                    <h3 className="text-lg font-semibold text-foreground">Filters</h3>
                    {hasActiveFilters && (
                        <span className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                            Active
                        </span>
                    )}
                </div>
                {hasActiveFilters && (
                    <button
                        onClick={onReset}
                        className="flex items-center gap-1 px-3 py-1 text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                        <XMarkIcon className="w-4 h-4" />
                        Clear All
                    </button>
                )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Transaction Type Filter */}
                <div className="space-y-2">
                    <label className="text-sm font-medium text-foreground">Type</label>
                    <select
                        value={filters.type || ''}
                        onChange={(e) => handleFilterChange('type', e.target.value || undefined)}
                        className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
                    >
                        <option value="">All Types</option>
                        {transactionTypes.map(type => (
                            <option key={type.value} value={type.value}>
                                {type.label}
                            </option>
                        ))}
                    </select>
                </div>

                {/* Status Filter */}
                <div className="space-y-2">
                    <label className="text-sm font-medium text-foreground">Status</label>
                    <select
                        value={filters.status || ''}
                        onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
                        className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
                    >
                        <option value="">All Statuses</option>
                        {transactionStatuses.map(status => (
                            <option key={status.value} value={status.value}>
                                {status.label}
                            </option>
                        ))}
                    </select>
                </div>

                {/* Date From Filter */}
                <div className="space-y-2">
                    <label className="text-sm font-medium text-foreground">From Date</label>
                    <div className="relative">
                        <input
                            type="date"
                            value={filters.dateFrom || ''}
                            onChange={(e) => handleFilterChange('dateFrom', e.target.value || undefined)}
                            className="w-full px-3 py-2 pl-10 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
                        />
                        <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    </div>
                </div>

                {/* Date To Filter */}
                <div className="space-y-2">
                    <label className="text-sm font-medium text-foreground">To Date</label>
                    <div className="relative">
                        <input
                            type="date"
                            value={filters.dateTo || ''}
                            onChange={(e) => handleFilterChange('dateTo', e.target.value || undefined)}
                            className="w-full px-3 py-2 pl-10 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
                        />
                        <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    </div>
                </div>
            </div>

            {/* Quick Filter Buttons */}
            <div className="mt-4 pt-4 border-t border-border">
                <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium text-foreground">Quick Filters:</span>
                </div>
                <div className="flex flex-wrap gap-2">
                    <button
                        onClick={() => {
                            const today = new Date();
                            const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                            onFiltersChange({
                                ...filters,
                                dateFrom: lastWeek.toISOString().split('T')[0],
                                dateTo: today.toISOString().split('T')[0]
                            });
                        }}
                        className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors"
                    >
                        Last 7 Days
                    </button>
                    <button
                        onClick={() => {
                            const today = new Date();
                            const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
                            onFiltersChange({
                                ...filters,
                                dateFrom: lastMonth.toISOString().split('T')[0],
                                dateTo: today.toISOString().split('T')[0]
                            });
                        }}
                        className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors"
                    >
                        Last 30 Days
                    </button>
                    <button
                        onClick={() => {
                            const today = new Date();
                            const lastQuarter = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
                            onFiltersChange({
                                ...filters,
                                dateFrom: lastQuarter.toISOString().split('T')[0],
                                dateTo: today.toISOString().split('T')[0]
                            });
                        }}
                        className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors"
                    >
                        Last 3 Months
                    </button>
                    <button
                        onClick={() => onFiltersChange({ ...filters, type: 'PURCHASE' })}
                        className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                            filters.type === 'PURCHASE' 
                                ? 'bg-primary text-primary-foreground' 
                                : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                        }`}
                    >
                        Purchases Only
                    </button>
                    <button
                        onClick={() => onFiltersChange({ ...filters, type: 'USAGE' })}
                        className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                            filters.type === 'USAGE' 
                                ? 'bg-primary text-primary-foreground' 
                                : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                        }`}
                    >
                        Usage Only
                    </button>
                </div>
            </div>
        </div>
    );
};

export default TransactionFilterComponent;
