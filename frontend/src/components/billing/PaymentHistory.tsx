import React, { useState, useEffect } from 'react';
import { 
    ArrowUpIcon, 
    ArrowDownIcon, 
    CreditCardIcon, 
    GiftIcon,
    ArrowPathIcon,
    CalendarIcon,
    DocumentArrowDownIcon,
    EyeIcon,
    CheckCircleIcon,
    ClockIcon,
    ExclamationCircleIcon,
    XCircleIcon
} from '@heroicons/react/24/outline';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { toast } from 'react-toastify';
import { PaymentTransaction, PaymentHistoryProps, TransactionFilters } from '@/types/billing';
import TransactionFilterComponent from './TransactionFilters';

const PaymentHistory: React.FC<PaymentHistoryProps> = ({ 
    className = "",
    showFilters = true,
    limit = 20,
    onTransactionClick
}) => {
    const [transactions, setTransactions] = useState<PaymentTransaction[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [pagination, setPagination] = useState({
        page: 1,
        limit: limit,
        total: 0,
        totalPages: 0
    });
    const [filters, setFilters] = useState<TransactionFilters>({
        page: 1,
        limit: limit
    });
    const axiosPrivate = useAxiosPrivate();

    const fetchTransactions = async (newFilters?: TransactionFilters) => {
        try {
            setLoading(true);
            setError(null);
            
            const queryFilters = newFilters || filters;
            const queryParams = new URLSearchParams();
            
            Object.entries(queryFilters).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                    queryParams.append(key, value.toString());
                }
            });

            const response = await axiosPrivate.get(`/api/credits/transactions?${queryParams.toString()}`);
            setTransactions(response.data.data.transactions);
            setPagination(response.data.data.pagination);
        } catch (err: any) {
            console.error('Error fetching transactions:', err);
            setError(err.response?.data?.message || 'Failed to fetch transaction history');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchTransactions();
    }, []);

    const handleFiltersChange = (newFilters: TransactionFilters) => {
        const updatedFilters = { ...newFilters, page: 1 };
        setFilters(updatedFilters);
        fetchTransactions(updatedFilters);
    };

    const handlePageChange = (newPage: number) => {
        const updatedFilters = { ...filters, page: newPage };
        setFilters(updatedFilters);
        fetchTransactions(updatedFilters);
    };

    const resetFilters = () => {
        const defaultFilters = { page: 1, limit: limit };
        setFilters(defaultFilters);
        fetchTransactions(defaultFilters);
    };

    const getTransactionIcon = (transaction: PaymentTransaction) => {
        switch (transaction.type) {
            case 'PURCHASE':
                return <ArrowDownIcon className="w-5 h-5 text-green-500" />;
            case 'USAGE':
                return <ArrowUpIcon className="w-5 h-5 text-red-500" />;
            case 'REFUND':
                return <ArrowDownIcon className="w-5 h-5 text-blue-500" />;
            case 'BONUS':
            case 'INITIAL_GRANT':
                return <GiftIcon className="w-5 h-5 text-purple-500" />;
            default:
                return <CreditCardIcon className="w-5 h-5 text-muted-foreground" />;
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'COMPLETED':
                return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
            case 'PENDING':
                return <ClockIcon className="w-4 h-4 text-yellow-500" />;
            case 'FAILED':
                return <XCircleIcon className="w-4 h-4 text-red-500" />;
            case 'CANCELLED':
                return <ExclamationCircleIcon className="w-4 h-4 text-gray-500" />;
            default:
                return <ClockIcon className="w-4 h-4 text-muted-foreground" />;
        }
    };

    const formatAmount = (amount: number) => {
        return `₹${(amount / 100).toFixed(2)}`;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getTransactionTypeLabel = (type: string) => {
        switch (type) {
            case 'PURCHASE': return 'Credit Purchase';
            case 'USAGE': return 'Credit Usage';
            case 'REFUND': return 'Refund';
            case 'BONUS': return 'Bonus Credits';
            case 'INITIAL_GRANT': return 'Welcome Credits';
            default: return type;
        }
    };

    if (loading && transactions.length === 0) {
        return (
            <div className={`bg-card rounded-lg border border-border p-6 ${className}`}>
                <div className="flex items-center gap-3 mb-4">
                    <CalendarIcon className="w-5 h-5 text-muted-foreground" />
                    <h3 className="text-lg font-semibold text-foreground">Payment History</h3>
                </div>
                <div className="flex items-center justify-center py-8">
                    <ArrowPathIcon className="w-6 h-6 animate-spin text-primary" />
                    <span className="ml-2 text-muted-foreground">Loading transactions...</span>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`bg-card rounded-lg border border-destructive/50 p-6 ${className}`}>
                <div className="flex items-center gap-3 mb-4">
                    <CalendarIcon className="w-5 h-5 text-destructive" />
                    <h3 className="text-lg font-semibold text-foreground">Payment History</h3>
                </div>
                <div className="text-center py-8">
                    <p className="text-destructive mb-3">{error}</p>
                    <button 
                        onClick={() => fetchTransactions()}
                        className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2 mx-auto"
                    >
                        <ArrowPathIcon className="w-4 h-4" />
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className={`space-y-4 ${className}`}>
            {/* Filters */}
            {showFilters && (
                <TransactionFilterComponent
                    filters={filters}
                    onFiltersChange={handleFiltersChange}
                    onReset={resetFilters}
                />
            )}

            {/* Transaction List */}
            <div className="bg-card rounded-lg border border-border">
                <div className="flex items-center justify-between p-4 border-b border-border">
                    <div className="flex items-center gap-3">
                        <CalendarIcon className="w-5 h-5 text-muted-foreground" />
                        <h3 className="text-lg font-semibold text-foreground">Payment History</h3>
                        <span className="px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded-full">
                            {pagination.total} transactions
                        </span>
                    </div>
                    {loading && (
                        <ArrowPathIcon className="w-5 h-5 animate-spin text-primary" />
                    )}
                </div>

                {transactions.length === 0 ? (
                    <div className="text-center py-12">
                        <CalendarIcon className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground mb-2">No transactions found</p>
                        <p className="text-sm text-muted-foreground">
                            {Object.keys(filters).length > 2 ? 'Try adjusting your filters' : 'Your transaction history will appear here'}
                        </p>
                    </div>
                ) : (
                    <div className="divide-y divide-border">
                        {transactions.map((transaction) => (
                            <div 
                                key={transaction._id} 
                                className="p-4 hover:bg-secondary/50 transition-colors cursor-pointer"
                                onClick={() => onTransactionClick?.(transaction)}
                            >
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 rounded-full bg-secondary">
                                            {getTransactionIcon(transaction)}
                                        </div>
                                        <div>
                                            <div className="flex items-center gap-2">
                                                <p className="font-medium text-foreground">
                                                    {getTransactionTypeLabel(transaction.type)}
                                                </p>
                                                {getStatusIcon(transaction.status)}
                                            </div>
                                            <p className="text-sm text-muted-foreground">
                                                {transaction.description}
                                            </p>
                                            <p className="text-xs text-muted-foreground">
                                                {formatDate(transaction.createdAt)}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="flex items-center gap-2">
                                            <span className={`font-semibold ${
                                                transaction.type === 'PURCHASE' || transaction.type === 'BONUS' || transaction.type === 'INITIAL_GRANT'
                                                    ? 'text-green-600 dark:text-green-400'
                                                    : 'text-red-600 dark:text-red-400'
                                            }`}>
                                                {transaction.type === 'PURCHASE' || transaction.type === 'BONUS' || transaction.type === 'INITIAL_GRANT' ? '+' : '-'}
                                                {transaction.creditAmount} credits
                                            </span>
                                            {transaction.payment?.amount && (
                                                <span className="text-sm text-muted-foreground">
                                                    ({formatAmount(transaction.payment.amount)})
                                                </span>
                                            )}
                                        </div>
                                        <p className="text-xs text-muted-foreground">
                                            Balance: {transaction.balanceAfter} credits
                                        </p>
                                        {transaction.payment?.razorpayPaymentId && (
                                            <div className="flex items-center gap-1 mt-1">
                                                <EyeIcon className="w-3 h-3 text-muted-foreground" />
                                                <span className="text-xs text-muted-foreground">
                                                    View Receipt
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}

                {/* Pagination */}
                {pagination.totalPages > 1 && (
                    <div className="flex items-center justify-between p-4 border-t border-border">
                        <p className="text-sm text-muted-foreground">
                            Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} transactions
                        </p>
                        <div className="flex items-center gap-2">
                            <button
                                onClick={() => handlePageChange(pagination.page - 1)}
                                disabled={pagination.page <= 1}
                                className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Previous
                            </button>
                            <span className="px-3 py-1 text-sm text-muted-foreground">
                                Page {pagination.page} of {pagination.totalPages}
                            </span>
                            <button
                                onClick={() => handlePageChange(pagination.page + 1)}
                                disabled={pagination.page >= pagination.totalPages}
                                className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Next
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default PaymentHistory;
