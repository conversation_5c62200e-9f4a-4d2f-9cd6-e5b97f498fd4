import React from 'react';
import { 
    XMarkIcon, 
    DocumentArrowDownIcon,
    CreditCardIcon,
    CalendarIcon,
    IdentificationIcon,
    BanknotesIcon,
    CheckCircleIcon
} from '@heroicons/react/24/outline';
import { ReceiptModalProps } from '@/types/billing';
import { useUser } from '@/contexts/userContext';

const ReceiptModal: React.FC<ReceiptModalProps> = ({
    isOpen,
    onClose,
    transaction,
    onDownload
}) => {
    const { user } = useUser();

    if (!isOpen) return null;

    const formatAmount = (amount: number) => {
        return `₹${(amount / 100).toFixed(2)}`;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const handleDownload = () => {
        // Create a printable receipt
        const receiptContent = `
            <html>
                <head>
                    <title>Payment Receipt - ${transaction.transactionId}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }
                        .logo { font-size: 24px; font-weight: bold; color: #333; }
                        .receipt-title { font-size: 18px; margin-top: 10px; }
                        .section { margin: 20px 0; }
                        .section-title { font-weight: bold; font-size: 16px; margin-bottom: 10px; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
                        .row { display: flex; justify-content: space-between; margin: 8px 0; }
                        .label { font-weight: bold; }
                        .value { text-align: right; }
                        .total { font-size: 18px; font-weight: bold; border-top: 2px solid #333; padding-top: 10px; margin-top: 20px; }
                        .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="logo">AegisScholar</div>
                        <div class="receipt-title">Payment Receipt</div>
                    </div>
                    
                    <div class="section">
                        <div class="section-title">Transaction Details</div>
                        <div class="row">
                            <span class="label">Transaction ID:</span>
                            <span class="value">${transaction.transactionId}</span>
                        </div>
                        <div class="row">
                            <span class="label">Payment ID:</span>
                            <span class="value">${transaction.payment?.razorpayPaymentId || 'N/A'}</span>
                        </div>
                        <div class="row">
                            <span class="label">Order ID:</span>
                            <span class="value">${transaction.payment?.razorpayOrderId || 'N/A'}</span>
                        </div>
                        <div class="row">
                            <span class="label">Date:</span>
                            <span class="value">${formatDate(transaction.createdAt)}</span>
                        </div>
                        <div class="row">
                            <span class="label">Status:</span>
                            <span class="value">${transaction.status}</span>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-title">Customer Information</div>
                        <div class="row">
                            <span class="label">Name:</span>
                            <span class="value">${user?.firstName} ${user?.lastName}</span>
                        </div>
                        <div class="row">
                            <span class="label">Email:</span>
                            <span class="value">${user?.email}</span>
                        </div>
                        <div class="row">
                            <span class="label">User ID:</span>
                            <span class="value">${user?.id}</span>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-title">Purchase Details</div>
                        <div class="row">
                            <span class="label">Package:</span>
                            <span class="value">${transaction.payment?.packageName || 'N/A'}</span>
                        </div>
                        <div class="row">
                            <span class="label">Credits Added:</span>
                            <span class="value">${transaction.creditAmount} credits</span>
                        </div>
                        <div class="row">
                            <span class="label">Amount Paid:</span>
                            <span class="value">${transaction.payment?.amount ? formatAmount(transaction.payment.amount) : 'N/A'}</span>
                        </div>
                        <div class="row">
                            <span class="label">Currency:</span>
                            <span class="value">${transaction.payment?.currency || 'INR'}</span>
                        </div>
                    </div>

                    <div class="total">
                        <div class="row">
                            <span class="label">Total Amount:</span>
                            <span class="value">${transaction.payment?.amount ? formatAmount(transaction.payment.amount) : 'N/A'}</span>
                        </div>
                    </div>

                    <div class="footer">
                        <p>Thank you for your purchase!</p>
                        <p>For support, contact <NAME_EMAIL></p>
                        <p>Generated on ${new Date().toLocaleDateString('en-IN')}</p>
                    </div>
                </body>
            </html>
        `;

        const printWindow = window.open('', '_blank');
        if (printWindow) {
            printWindow.document.write(receiptContent);
            printWindow.document.close();
            printWindow.print();
        }

        if (onDownload) {
            onDownload();
        }
    };

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-card rounded-lg border border-border max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-border">
                    <div className="flex items-center gap-3">
                        <CreditCardIcon className="w-6 h-6 text-primary" />
                        <h2 className="text-xl font-semibold text-foreground">Payment Receipt</h2>
                    </div>
                    <button
                        onClick={onClose}
                        className="p-2 hover:bg-secondary rounded-lg transition-colors"
                    >
                        <XMarkIcon className="w-5 h-5 text-muted-foreground" />
                    </button>
                </div>

                {/* Receipt Content */}
                <div className="p-6 space-y-6">
                    {/* Transaction Status */}
                    <div className="flex items-center justify-center gap-2 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <CheckCircleIcon className="w-6 h-6 text-green-600 dark:text-green-400" />
                        <span className="text-green-800 dark:text-green-200 font-medium">
                            Payment Successful
                        </span>
                    </div>

                    {/* Transaction Details */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                            Transaction Details
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-3">
                                <div className="flex items-center gap-2">
                                    <IdentificationIcon className="w-4 h-4 text-muted-foreground" />
                                    <span className="text-sm text-muted-foreground">Transaction ID</span>
                                </div>
                                <p className="font-mono text-sm bg-secondary p-2 rounded">
                                    {transaction.transactionId}
                                </p>
                            </div>
                            <div className="space-y-3">
                                <div className="flex items-center gap-2">
                                    <CalendarIcon className="w-4 h-4 text-muted-foreground" />
                                    <span className="text-sm text-muted-foreground">Date & Time</span>
                                </div>
                                <p className="text-sm">
                                    {formatDate(transaction.createdAt)}
                                </p>
                            </div>
                        </div>

                        {transaction.payment && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-3">
                                    <div className="flex items-center gap-2">
                                        <CreditCardIcon className="w-4 h-4 text-muted-foreground" />
                                        <span className="text-sm text-muted-foreground">Payment ID</span>
                                    </div>
                                    <p className="font-mono text-sm bg-secondary p-2 rounded">
                                        {transaction.payment.razorpayPaymentId}
                                    </p>
                                </div>
                                <div className="space-y-3">
                                    <div className="flex items-center gap-2">
                                        <BanknotesIcon className="w-4 h-4 text-muted-foreground" />
                                        <span className="text-sm text-muted-foreground">Order ID</span>
                                    </div>
                                    <p className="font-mono text-sm bg-secondary p-2 rounded">
                                        {transaction.payment.razorpayOrderId}
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Purchase Details */}
                    {transaction.payment && (
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                                Purchase Details
                            </h3>
                            <div className="bg-secondary/50 rounded-lg p-4 space-y-3">
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Package:</span>
                                    <span className="font-medium">{transaction.payment.packageName}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Credits Added:</span>
                                    <span className="font-medium text-green-600 dark:text-green-400">
                                        +{transaction.creditAmount} credits
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Amount Paid:</span>
                                    <span className="font-medium">{formatAmount(transaction.payment.amount)}</span>
                                </div>
                                <div className="flex justify-between border-t border-border pt-3">
                                    <span className="font-semibold">Total:</span>
                                    <span className="font-semibold text-lg">
                                        {formatAmount(transaction.payment.amount)}
                                    </span>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Customer Information */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                            Customer Information
                        </h3>
                        <div className="space-y-2">
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Name:</span>
                                <span>{user?.firstName} {user?.lastName}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Email:</span>
                                <span>{user?.email}</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between p-6 border-t border-border bg-secondary/20">
                    <p className="text-sm text-muted-foreground">
                        Thank you for your purchase!
                    </p>
                    <button
                        onClick={handleDownload}
                        className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                    >
                        <DocumentArrowDownIcon className="w-4 h-4" />
                        Download Receipt
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ReceiptModal;
