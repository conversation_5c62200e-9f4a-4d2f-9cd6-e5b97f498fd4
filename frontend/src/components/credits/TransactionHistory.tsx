import React, { useState, useEffect } from 'react';
import { 
    ArrowUpIcon, 
    ArrowDownIcon, 
    CreditCardIcon, 
    GiftIcon,
    ArrowPathIcon,
    CalendarIcon
} from '@heroicons/react/24/outline';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { toast } from 'react-toastify';

interface Transaction {
    _id: string;
    transactionId: string;
    type: 'PURCHASE' | 'USAGE' | 'REFUND' | 'BONUS' | 'INITIAL_GRANT';
    status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
    creditAmount: number;
    balanceBefore: number;
    balanceAfter: number;
    description: string;
    createdAt: string;
    completedAt?: string;
    payment?: {
        amount: number;
        packageName: string;
        razorpayPaymentId: string;
    };
    usage?: {
        feature: string;
        description: string;
        relatedId: string;
    };
}

interface TransactionHistoryProps {
    className?: string;
    limit?: number;
}

const TransactionHistory: React.FC<TransactionHistoryProps> = ({ 
    className = "",
    limit = 20
}) => {
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const axiosPrivate = useAxiosPrivate();

    const fetchTransactions = async () => {
        try {
            setLoading(true);
            setError(null);
            const response = await axiosPrivate.get(`/api/credits/transactions?limit=${limit}`);
            setTransactions(response.data.data.transactions);
        } catch (err: any) {
            console.error('Error fetching transactions:', err);
            setError(err.response?.data?.message || 'Failed to fetch transaction history');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchTransactions();
    }, [limit]);

    const getTransactionIcon = (type: string) => {
        switch (type) {
            case 'PURCHASE':
                return <ArrowDownIcon className="w-4 h-4 text-green-600 dark:text-green-400" />;
            case 'USAGE':
                return <ArrowUpIcon className="w-4 h-4 text-red-600 dark:text-red-400" />;
            case 'INITIAL_GRANT':
            case 'BONUS':
                return <GiftIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />;
            case 'REFUND':
                return <ArrowDownIcon className="w-4 h-4 text-green-600 dark:text-green-400" />;
            default:
                return <CreditCardIcon className="w-4 h-4 text-muted-foreground" />;
        }
    };

    const getTransactionColor = (type: string) => {
        switch (type) {
            case 'PURCHASE':
            case 'REFUND':
                return 'text-green-600 dark:text-green-400';
            case 'USAGE':
                return 'text-red-600 dark:text-red-400';
            case 'INITIAL_GRANT':
            case 'BONUS':
                return 'text-blue-600 dark:text-blue-400';
            default:
                return 'text-muted-foreground';
        }
    };

    const formatTransactionType = (type: string) => {
        switch (type) {
            case 'PURCHASE':
                return 'Credit Purchase';
            case 'USAGE':
                return 'Credit Used';
            case 'INITIAL_GRANT':
                return 'Welcome Bonus';
            case 'BONUS':
                return 'Bonus Credits';
            case 'REFUND':
                return 'Refund';
            default:
                return type;
        }
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-IN', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    if (loading) {
        return (
            <div className={`bg-card rounded-lg border border-border p-6 ${className}`}>
                <div className="flex items-center gap-3 mb-4">
                    <CalendarIcon className="w-5 h-5 text-muted-foreground" />
                    <h3 className="text-lg font-semibold text-foreground">Transaction History</h3>
                </div>
                <div className="space-y-3">
                    {[1, 2, 3].map(i => (
                        <div key={i} className="flex items-center gap-3 p-3 animate-pulse">
                            <div className="w-8 h-8 bg-muted rounded-full"></div>
                            <div className="flex-1">
                                <div className="h-4 bg-muted rounded mb-2"></div>
                                <div className="h-3 bg-muted rounded w-2/3"></div>
                            </div>
                            <div className="h-4 bg-muted rounded w-16"></div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`bg-card rounded-lg border border-destructive/50 p-6 ${className}`}>
                <div className="flex items-center gap-3 mb-4">
                    <CalendarIcon className="w-5 h-5 text-destructive" />
                    <h3 className="text-lg font-semibold text-foreground">Transaction History</h3>
                </div>
                <div className="text-center py-8">
                    <p className="text-destructive mb-3">{error}</p>
                    <button 
                        onClick={fetchTransactions}
                        className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2 mx-auto"
                    >
                        <ArrowPathIcon className="w-4 h-4" />
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className={`bg-card rounded-lg border border-border p-6 ${className}`}>
            <div className="flex items-center gap-3 mb-4">
                <CalendarIcon className="w-5 h-5 text-muted-foreground" />
                <h3 className="text-lg font-semibold text-foreground">Transaction History</h3>
            </div>

            {transactions.length === 0 ? (
                <div className="text-center py-8">
                    <CreditCardIcon className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
                    <p className="text-muted-foreground">No transactions yet</p>
                    <p className="text-sm text-muted-foreground">Your credit transactions will appear here</p>
                </div>
            ) : (
                <div className="space-y-3">
                    {transactions.map((transaction) => (
                        <div 
                            key={transaction._id}
                            className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors"
                        >
                            <div className="p-2 bg-muted rounded-full">
                                {getTransactionIcon(transaction.type)}
                            </div>
                            
                            <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                    <p className="font-medium text-foreground truncate">
                                        {formatTransactionType(transaction.type)}
                                    </p>
                                    {transaction.payment?.packageName && (
                                        <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                                            {transaction.payment.packageName}
                                        </span>
                                    )}
                                </div>
                                <p className="text-sm text-muted-foreground truncate">
                                    {transaction.description}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                    {formatDate(transaction.createdAt)}
                                </p>
                            </div>
                            
                            <div className="text-right">
                                <p className={`font-semibold ${getTransactionColor(transaction.type)}`}>
                                    {transaction.type === 'USAGE' ? '-' : '+'}
                                    {transaction.creditAmount}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                    Balance: {transaction.balanceAfter}
                                </p>
                                {transaction.payment?.amount && (
                                    <p className="text-xs text-muted-foreground">
                                        ₹{(transaction.payment.amount / 100).toFixed(2)}
                                    </p>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default TransactionHistory;
