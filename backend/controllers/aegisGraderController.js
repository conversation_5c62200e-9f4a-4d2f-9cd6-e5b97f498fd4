import { validationResult } from "express-validator";
import { spawn } from "child_process";
import fs from "fs";
import { parseStringPromise } from "xml2js";
import AegisGrader from "../models/AegisGrader.js";
import CreditService from "../services/creditService.js";
import { USAGE_TYPES } from "../models/CreditTransaction.js";

import S3 from 'aws-sdk/clients/s3.js';
import { randomUUID } from 'crypto';

const s3 = new S3({
    apiVersion: '2006-03-01',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: "ap-south-1",
    signatureVersion: 'v4',
});

export const getAllSubmissions = async (req, res) => {
    try {
        const { userId } = req.params;
        const submissions = await AegisGrader.find({ createdBy: userId }).sort({ timestamp: -1 });
        if (!submissions || submissions.length === 0) {
            return res.status(404).json({ message: "No submissions found" });
        }
        return res.status(200).json({
            message: "Submissions retrieved successfully",
            submissions: submissions.map(submission => ({
                id: submission._id,
                testDetails: submission.testDetails,
                answerSheets: submission.answerSheets,
                questionPaper: submission.questionPaper,
                rubric: submission.rubric,
                createdAt: submission.createdAt
            }))
        });
    } catch (error) {
        console.error("Error retrieving submissions:", error);
        return res.status(500).json({
            message: "Internal Server Error",
            error: error.message || "Unknown error occurred"
        });
    }
};

export const submitForGrading = async (req, res) => {
    const tempFilePath = "./aegis_grader/tempFile.json";
    const scriptPath = "aegis_grader/main.py";

    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(401).json({ errors: errors.array() });
        }
        const { testDetails, answerSheets, questionPaper, rubric } = req.body;

        if (!answerSheets || answerSheets.length === 0) {
            return res.status(400).json({ message: "No answer sheets provided" });
        }
        if (!testDetails || !testDetails.className || !testDetails.subject) {
            return res.status(400).json({ message: "Invalid test details" });
        }

        try {
            fs.writeFileSync(tempFilePath, JSON.stringify({ testDetails, answerSheets, questionPaper, rubric }));
            console.error("Temp input file written successfully:", tempFilePath);
        } catch (writeError) {
            console.error("Error writing temp input file:", writeError);
            return res.status(500).json({ message: "Error writing temporary input file", error: writeError.message });
        }

        console.error("Executing Python script:", scriptPath);
        const rawOutputString = await runPythonScript(scriptPath);

        const parsedEvaluations = [];
        const evalMatches = rawOutputString.match(/<evaluation>[\s\S]*?<\/evaluation>/g);

        if (evalMatches && evalMatches.length > 0) {
            console.error(`Found ${evalMatches.length} evaluation block(s) using regex.`);
            for (let i = 0; i < evalMatches.length; i++) {
                const evalXmlString = evalMatches[i];
                console.error(`Attempting to parse evaluation block ${i + 1}...`);
                try {
                    const parsedXml = await parseStringPromise(evalXmlString, {
                        explicitArray: false,
                        tagNameProcessors: [],
                        attrNameProcessors: [],
                        valueProcessors: [],
                        attrValueProcessors: [],
                        charkey: "_",
                        trim: true,
                        normalizeTags: true,
                        normalize: true,
                        mergeAttrs: true
                    });
                    parsedEvaluations.push(parsedXml);
                    console.error(`Successfully parsed evaluation block ${i + 1}.`);
                } catch (xmlError) {
                    console.error(`Error parsing XML block ${i + 1}:`, xmlError.message);
                    console.error("Malformed XML block content:\n", evalXmlString);
                }
            }
        } else {
            console.warn("No complete <evaluation>...</evaluation> tags found in the Python script output.");
        }

        console.error(`Returning ${parsedEvaluations.length} parsed evaluations.`);

        // Save evaluation results to database
        const savedSubmissions = [];
        for (let i = 0; i < parsedEvaluations.length; i++) {
            const newAegisGrader = new AegisGrader({
                testDetails: {
                    className: testDetails.className,
                    subject: testDetails.subject,
                    date: testDetails.date || new Date().toISOString()
                },
                answerSheets: answerSheets.map(sheet => ({
                    id: sheet.id,
                    studentName: sheet.studentName,
                    rollNumber: sheet.rollNumber,
                    pdfUrl: sheet.pdfUrl,
                    timestamp: sheet.timestamp,
                    className: testDetails.className,
                    evaluationResult: parsedEvaluations[i] || {}
                })),
                questionPaper: {
                    type: questionPaper.type || "questionPaper",
                    pdfUrl: questionPaper.pdfUrl || "",
                    timestamp: questionPaper.timestamp || Date.now()
                },
                rubric: {
                    type: rubric.type || "rubric",
                    pdfUrl: rubric.pdfUrl || "",
                    timestamp: rubric.timestamp || Date.now()
                }
            });
            await newAegisGrader.save();
            savedSubmissions.push(newAegisGrader);
            console.error(`Aegis Grader submission saved successfully for answer sheet ${i} :`, newAegisGrader);
        }

        // Deduct credits after successful evaluation
        try {
            const userId = req.user.id;
            const userType = req.user.type;
            const creditsToDeduct = answerSheets.length; // 1 credit per answer sheet

            const deductionResult = await CreditService.deductCredits(userId, userType, creditsToDeduct, {
                feature: USAGE_TYPES.AEGIS_GRADER,
                description: `AegisGrader evaluation for ${testDetails.subject} - ${testDetails.className}`,
                relatedId: savedSubmissions[0]._id.toString(),
                metadata: {
                    testDetails,
                    answerSheetCount: answerSheets.length,
                    evaluationCount: parsedEvaluations.length
                }
            });

            console.log(`Successfully deducted ${creditsToDeduct} credits from user ${userId}. New balance: ${deductionResult.newBalance}`);

            return res.status(200).json({
                message: "Aegis Grader executed successfully",
                evaluations: parsedEvaluations,
                creditsDeducted: creditsToDeduct,
                remainingCredits: deductionResult.newBalance
            });

        } catch (creditError) {
            console.error('Error deducting credits after successful evaluation:', creditError);

            // Even if credit deduction fails, return success since evaluation completed
            // This prevents double-charging if there's a temporary issue
            return res.status(200).json({
                message: "Aegis Grader executed successfully",
                evaluations: parsedEvaluations,
                warning: "Credit deduction may have failed. Please contact support if credits were not properly deducted."
            });
        }
    } catch (error) {
        console.error("Error during AegisGrader submission process:", error);
        return res.status(500).json({
            message: "Internal Server Error during grading",
            error: error.message || "Unknown error occurred"
        });
    } finally {
        fs.unlink(tempFilePath, (err) => {
            if (err) {
                console.error("Error deleting temp input file:", err);
            } else {
                console.error("Temp input file deleted successfully:", tempFilePath);
            }
        });
    }
};

export const getPresignedUrl = async (req, res) => {
    try {
        const { questionPaperFile, rubricFile, answerSheets, fileType } = req.body;
        if ((!questionPaperFile & !rubricFile) || !answerSheets || !fileType) {
            return res.status(400).json({ message: "All File names and type are required" });
        }

        let uploadUrlQuestion;
        let uploadUrlRubric;
        let questionPaperKey;
        let rubricKey;

        let manifestInfo = { files: [] };

        console.log(`[Mehul] [INFO] got filename: ${questionPaperFile} and fileType: ${fileType}`);
        console.log(`[Mehul] [INFO] got s3 region: ${s3.config.region}`);
        console.log(`[Mehul] [INFO] got answer sheet names: ${answerSheets.join(",")}`);

        if (questionPaperFile) {
            questionPaperKey = `${questionPaperFile}-${randomUUID()}.pdf`;
            const questionFileManifest = {
                fileName: questionPaperFile,
                fileType: fileType,
                key: questionPaperKey,
                filePurpose: "question_paper",
                timestamp: Date.now(),
            }

            manifestInfo.files.push(questionFileManifest);

            const s3ParamsQuestion = {
                Bucket: process.env.UPLOAD_BUCKET_NAME,
                Key: questionPaperKey,
                Expires: 60, // URL valid for 60 seconds
                ContentType: fileType,
                ACL: 'private'
            }

            uploadUrlQuestion = await s3.getSignedUrl('putObject', s3ParamsQuestion);

        }

        if (rubricFile) {
            rubricKey = `${rubricFile}-${randomUUID()}.pdf`;
            console.log("[Mehul][INFO] got rubric key: ", rubricKey);

            const rubricFileManifest = {
                fileName: rubricFile,
                fileType: fileType,
                key: rubricKey,
                filePurpose: "rubric",
                timestamp: Date.now(),
            }

            manifestInfo.files.push(rubricFileManifest);
            const s3ParamsRubric = {
                Bucket: process.env.UPLOAD_BUCKET_NAME,
                Key: rubricKey,
                Expires: 60, // URL valid for 60 seconds
                ContentType: fileType,
                ACL: 'private'
            }

            uploadUrlRubric = await s3.getSignedUrl('putObject', s3ParamsRubric);
        }

        // create a presigned url for each answer sheet to upload to s3
        let answerSheetUrls = [];
        for (const sheet of answerSheets) {
            console.log("[Mehul][INFO] got sheet: ", sheet);
            const sheetKey = `${sheet.filename}-${randomUUID()}.pdf`;
            console.log("[Mehul][INFO] got sheet key: ", sheetKey);
            const answerSheetManifest = {
                fileName: sheet.filename,
                fileType: fileType,
                key: sheetKey,
                filePurpose: "answer_sheet",
                studentName: sheet.studentName,
                rollNumber: sheet.rollNumber,
                timestamp: Date.now(),
            }

            manifestInfo.files.push(answerSheetManifest);
            const s3ParamsSheet = {
                Bucket: process.env.UPLOAD_BUCKET_NAME,
                Key: sheetKey,
                Expires: 60, // URL valid for 60 seconds
                ContentType: fileType,
                ACL: 'private'
            }
            const uploadUrlSheet = await s3.getSignedUrl('putObject', s3ParamsSheet);
            answerSheetUrls.push({ sheetName: sheet.filename, uploadUrl: uploadUrlSheet, key: sheetKey });
        }

        const manifestKey = `manifest-${randomUUID()}.json`;
        const manifestParams = {
            Bucket: process.env.UPLOAD_BUCKET_NAME,
            Key: manifestKey,
            Expires: 60, // URL valid for 60 seconds
            ContentType: 'application/json',
            ACL: 'private'
        }

        const uploadUrlManifest = await s3.getSignedUrl('putObject', manifestParams);
        console.log("[Mehul][INFO] got manifest presigned: ", uploadUrlManifest);

        return res.status(200).json({
            message: "Presigned URL generated successfully",
            uploadUrlQuestion: uploadUrlQuestion,
            uploadUrlRubric: uploadUrlRubric,
            uploadUrlSheet: answerSheetUrls,
            fileNames: [questionPaperKey, rubricKey],
            manifest: manifestInfo,
            uploadUrlManifest: uploadUrlManifest
        });
    } catch (error) {
        console.error("Error generating presigned url: ", error);
        return res.status(500).json({
            message: "Internal Server Error",
            error: error.message || "Unknown error occurred"
        });
    }

}

async function runPythonScript(scriptPath) {
    return new Promise((resolve, reject) => {
        const pythonProcess = spawn("python3", [scriptPath]);
        let output = "";
        let errorOutput = "";

        pythonProcess.stdout.on("data", (data) => {
            output += data.toString();
        });
        pythonProcess.stderr.on("data", (data) => {
            errorOutput += data.toString();
            console.error(`Python stderr: ${data}`);
        });
        pythonProcess.on("error", (error) => {
            console.error(`Failed to start Python process: ${error.message}`);
            reject(new Error(`Failed to start Python process: ${error.message}`));
        });
        pythonProcess.on("close", (code) => {
            console.error(`Python process exited with code ${code}`);
            if (code !== 0) {
                reject(new Error(`Python script exited with code ${code}. Stderr: ${errorOutput}`));
            } else {
                resolve(output);
            }
        });
    });
}

